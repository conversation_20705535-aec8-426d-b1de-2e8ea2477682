"use client";
import React from "react";
import { RiSparklingFill } from "@remixicon/react";
import LogoSite from "@/app/components/base/logo/logo-site";

const Header = () => {
  return (
    <div className="flex w-full items-center justify-between p-6 md:p-8">
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2 backdrop-blur-sm bg-white/40 rounded-2xl px-4 py-2 border border-white/30 shadow-lg">
          <LogoSite />
          <div className="flex items-center gap-2">
            <span className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              数智AI应用开发平台
            </span>
            <RiSparklingFill className="w-5 h-5 text-blue-500 animate-pulse" />
          </div>
        </div>
      </div>

      {/* 语言选择器 - 如果需要的话可以取消注释 */}
      {/* <div className="backdrop-blur-sm bg-white/40 rounded-xl border border-white/30">
        <Select
          value={locale}
          items={languages.filter(item => item.supported)}
          onChange={(value) => {
            setLocaleOnClient(value as Locale)
          }}
        />
      </div> */}
    </div>
  );
};

export default Header;
