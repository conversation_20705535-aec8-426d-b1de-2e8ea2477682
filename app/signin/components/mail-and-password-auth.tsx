import Link from "next/link";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useRouter, useSearchParams } from "next/navigation";
import { useContext } from "use-context-selector";
import {
  RiMailLine,
  RiLockLine,
  RiEyeLine,
  RiEyeOffLine,
  RiShieldCheckLine,
  RiRefreshLine,
} from "@remixicon/react";
import Button from "@/app/components/base/button";
import Toast from "@/app/components/base/toast";
import { emailRegex } from "@/config";
import { login, fetchCaptcha, verifyCaptcha } from "@/service/common";
import Input from "@/app/components/base/input";
import I18NContext from "@/context/i18n";
import { noop } from "lodash-es";
import { EncryptByAES } from "@/utils/crypto";

type MailAndPasswordAuthProps = {
  isInvite: boolean;
  isEmailSetup: boolean;
  allowRegistration: boolean;
};

const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{8,}$/;

export default function MailAndPasswordAuth({
  isInvite,
  isEmailSetup,
  allowRegistration,
}: MailAndPasswordAuthProps) {
  const { t } = useTranslation();
  const { locale } = useContext(I18NContext);
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showPassword, setShowPassword] = useState(false);
  const emailFromLink = decodeURIComponent(searchParams.get("email") || "");
  const [email, setEmail] = useState(emailFromLink);
  const [password, setPassword] = useState("");
  const [captchaCode, setCaptchaCode] = useState("");
  const [captchaData, setCaptchaData] = useState({
    session_id: "",
    captcha_image_base64: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const handleEmailPasswordLogin = async () => {
    if (!email) {
      Toast.notify({ type: "error", message: t("login.error.emailEmpty") });
      return;
    }
    if (!emailRegex.test(email)) {
      Toast.notify({
        type: "error",
        message: t("login.error.emailInValid"),
      });
      return;
    }
    if (!password?.trim()) {
      Toast.notify({ type: "error", message: t("login.error.passwordEmpty") });
      return;
    }
    if (!passwordRegex.test(password)) {
      Toast.notify({
        type: "error",
        message: t("login.error.passwordInvalid"),
      });
      return;
    }
    //校验验证码
    const { result, message } = await verifyCaptcha({
      url: "/captcha/verify",
      body: {
        session_id: captchaData.session_id,
        captcha: captchaCode,
      },
    });
    if (result !== "success") {
      Toast.notify({
        type: "error",
        message: message || t("login.error.verificationInvalid"),
      });
      return;
    }
    try {
      setIsLoading(true);
      const loginData: Record<string, any> = {
        email,
        password: EncryptByAES({ data: password }),
        language: locale,
        remember_me: true,
      };
      if (isInvite)
        loginData.invite_token = decodeURIComponent(
          searchParams.get("invite_token") as string
        );
      const res = await login({
        url: "/login",
        body: loginData,
      });
      if (res.result === "success") {
        if (isInvite) {
          router.replace(`/signin/invite-settings?${searchParams.toString()}`);
        } else {
          localStorage.setItem("console_token", res.data.access_token);
          localStorage.setItem("refresh_token", res.data.refresh_token);
          // Extend Begin  ----------------
          // 如果本地浏览器缓存数据存在重定向url，则跳转到重定向url
          if (localStorage.getItem("redirect_url")) {
            const redirectUrl = localStorage.getItem("redirect_url");
            localStorage.removeItem("redirect_url");
            router.replace(redirectUrl as string);
            return;
          }
          router.replace("/explore/apps-center-extend");
          // Extend End  ----------------
        }
      } else if (res.code === "account_not_found") {
        if (allowRegistration) {
          const params = new URLSearchParams();
          params.append("email", encodeURIComponent(email));
          params.append("token", encodeURIComponent(res.data));
          router.replace(`/reset-password/check-code?${params.toString()}`);
        } else {
          Toast.notify({
            type: "error",
            message: t("login.error.registrationNotAllowed"),
          });
        }
      } else {
        Toast.notify({
          type: "error",
          message: res.data,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const fetchImgCode = async () => {
    const { result, data } = await fetchCaptcha({
      url: "/captcha",
    });
    if (result === "success") {
      setCaptchaData(data);
    }
  };

  useEffect(() => {
    fetchImgCode();
    const timer = setInterval(() => {
      fetchImgCode();
    }, 60000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  return (
    <div className="space-y-6">
      {/* 标题区域 */}
      <div className="text-center space-y-2">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg mb-4">
          <RiShieldCheckLine className="w-8 h-8 text-white" />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 tracking-tight">
          赛力斯统一认证登录
        </h1>
        <p className="text-sm text-gray-600">请输入您的凭据以访问系统</p>
      </div>

      <form onSubmit={noop} className="space-y-5">
        {/* 邮箱输入 */}
        <div className="space-y-2">
          <label
            htmlFor="email"
            className="flex items-center gap-2 text-sm font-medium text-gray-700"
          >
            <RiMailLine className="w-4 h-4 text-gray-500" />
            {t("login.email")}
          </label>
          <div className="relative">
            <Input
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isInvite}
              id="email"
              type="email"
              autoComplete="email"
              placeholder={t("login.emailPlaceholder") || ""}
              tabIndex={1}
              className="pl-10 h-12 rounded-xl border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
            />
            <RiMailLine className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          </div>
        </div>

        {/* 密码输入 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label
              htmlFor="password"
              className="flex items-center gap-2 text-sm font-medium text-gray-700"
            >
              <RiLockLine className="w-4 h-4 text-gray-500" />
              {t("login.password")}
            </label>
            <Link
              href={`/reset-password?${searchParams.toString()}`}
              className={`text-xs transition-colors duration-200 ${
                isEmailSetup
                  ? "text-blue-600 hover:text-blue-700 hover:underline"
                  : "pointer-events-none text-gray-400"
              }`}
              tabIndex={isEmailSetup ? 0 : -1}
              aria-disabled={!isEmailSetup}
            >
              {t("login.forget")}
            </Link>
          </div>
          <div className="relative">
            <Input
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") handleEmailPasswordLogin();
              }}
              type={showPassword ? "text" : "password"}
              autoComplete="current-password"
              placeholder={t("login.passwordPlaceholder") || ""}
              tabIndex={2}
              className="pl-10 pr-12 h-12 rounded-xl border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
            />
            <RiLockLine className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-md hover:bg-gray-100 transition-colors duration-200"
            >
              {showPassword ? (
                <RiEyeOffLine className="w-5 h-5 text-gray-500" />
              ) : (
                <RiEyeLine className="w-5 h-5 text-gray-500" />
              )}
            </button>
          </div>
        </div>

        {/* 验证码输入 */}
        <div className="space-y-2">
          <label
            htmlFor="verificationCode"
            className="flex items-center gap-2 text-sm font-medium text-gray-700"
          >
            <RiShieldCheckLine className="w-4 h-4 text-gray-500" />
            {t("login.verification")}
          </label>
          <div className="flex items-center gap-3">
            <div className="flex-1 relative">
              <Input
                value={captchaCode}
                onChange={(e) => setCaptchaCode(e.target.value)}
                id="verificationCode"
                placeholder={t("login.verificationPlaceholder") || ""}
                tabIndex={3}
                className="pl-10 h-12 rounded-xl border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
              />
              <RiShieldCheckLine className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            </div>
            <div className="relative group">
              <img
                src={`data:image/png;base64,${captchaData["captcha_image_base64"]}`}
                alt={t("login.verification") || ""}
                className="h-12 w-24 rounded-lg border border-gray-200 cursor-pointer hover:border-gray-300 transition-colors duration-200 object-cover"
                onClick={fetchImgCode}
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200 flex items-center justify-center">
                <RiRefreshLine className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
              </div>
            </div>
          </div>
        </div>

        {/* 登录按钮 */}
        <div className="pt-2">
          <Button
            tabIndex={4}
            variant="primary"
            onClick={handleEmailPasswordLogin}
            disabled={isLoading || !email || !password || !captchaCode}
            className="w-full h-12 rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                登录中...
              </div>
            ) : (
              t("login.signBtn")
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
