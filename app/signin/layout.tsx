import Header from "./_header";

export default async function SignInLayout({ children }: any) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/20 to-cyan-400/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-300/10 to-purple-300/10 rounded-full blur-3xl"></div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex min-h-screen w-full justify-center p-4 md:p-6">
        <div className="flex w-full max-w-6xl shrink-0 flex-col">
          {/* Header */}
          <Header />

          {/* 登录表单容器 */}
          <div className="flex w-full grow items-center justify-center px-4 py-8">
            <div className="w-full max-w-md">
              <div className="backdrop-blur-xl bg-white/80 border border-white/20 rounded-3xl shadow-2xl shadow-blue-500/10 p-8 md:p-10">
                {children}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center py-6 px-4">
            <p className="text-xs text-gray-500/80 backdrop-blur-sm bg-white/30 rounded-full px-4 py-2 inline-block border border-white/20">
              © {new Date().getFullYear()} 北京赛力斯智行科技有限公司 版权所有
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
